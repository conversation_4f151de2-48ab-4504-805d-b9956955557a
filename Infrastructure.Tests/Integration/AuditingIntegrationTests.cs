using Domain.Entities;
using Domain.System;
using Infrastructure.Persistence;
using Infrastructure.Tests.Mocks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Infrastructure.Tests.Integration;

/// <summary>
/// Integrační testy pro ov<PERSON><PERSON><PERSON><PERSON> auditování a trackingu
/// </summary>
public class AuditingIntegrationTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly ServiceProvider _serviceProvider;

    public AuditingIntegrationTests()
    {
        var services = new ServiceCollection();
        
        // Registrace mock služeb
        services.AddScoped<Application.Abstraction.ICurrentUserService, Infrastructure.Tests.Mocks.MockCurrentUserService>();
        services.AddScoped<Application.Services.Events.DomainEventPublisher, Infrastructure.Tests.Mocks.MockDomainEventPublisher>();

        // Registrace interceptorů
        services.AddScoped<Infrastructure.Persistence.Interceptors.TrackableEntityInterceptor>();
        services.AddScoped<Infrastructure.Persistence.Interceptors.AuditableEntityInterceptor>();

        // Registrace DbContext s in-memory databází a interceptory
        services.AddDbContext<ApplicationDbContext>((serviceProvider, options) =>
        {
            var trackableInterceptor = serviceProvider.GetRequiredService<Infrastructure.Persistence.Interceptors.TrackableEntityInterceptor>();
            var auditableInterceptor = serviceProvider.GetRequiredService<Infrastructure.Persistence.Interceptors.AuditableEntityInterceptor>();

            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                   .AddInterceptors(trackableInterceptor, auditableInterceptor);
        });
        
        _serviceProvider = services.BuildServiceProvider();
        _context = _serviceProvider.GetRequiredService<ApplicationDbContext>();
        
        // Zajištění vytvoření databáze
        _context.Database.EnsureCreated();
    }

    [Fact]
    public async Task SampleEntity_WithAuditableAttribute_ShouldCreateAuditTrail()
    {
        // Arrange
        var entity = new SampleEntity
        {
            Id = 0, // Bude nastaveno automaticky při uložení
            Name = "Test Entity",
            Description = "Test Description",
            Age = 25,
            DateOfBirth = new DateTime(1998, 1, 1),
            IsActive = true,
            InternalNotes = "This should not be audited"
        };

        // Act
        _context.SampleEntity.Add(entity);
        await _context.SaveChangesAsync();

        // Assert
        var auditTrails = await _context.AuditTrails.ToListAsync();
        Assert.Single(auditTrails);
        
        var auditTrail = auditTrails.First();
        Assert.Equal("SampleEntity", auditTrail.EntityName);
        Assert.Equal(entity.Id.ToString(), auditTrail.EntityId);
        Assert.Equal("Added", auditTrail.Operation);
        Assert.NotNull(auditTrail.ChangesJson);
        
        // Ověření, že InternalNotes není v audit logu (má [NotAudited] atribut)
        Assert.DoesNotContain("InternalNotes", auditTrail.ChangesJson);
        
        // Ověření, že ostatní vlastnosti jsou v audit logu
        Assert.Contains("Name", auditTrail.ChangesJson);
        Assert.Contains("Description", auditTrail.ChangesJson);
    }

    [Fact]
    public async Task SampleEntity_Update_ShouldCreateAuditTrailAndUpdateTracking()
    {
        // Arrange
        var entity = new SampleEntity
        {
            Id = 0, // Bude nastaveno automaticky při uložení
            Name = "Original Name",
            Description = "Original Description",
            Age = 25,
            DateOfBirth = new DateTime(1998, 1, 1),
            IsActive = true
        };

        _context.SampleEntity.Add(entity);
        await _context.SaveChangesAsync();

        var originalCreatedAt = entity.CreatedAt;
        var originalCreatedBy = entity.CreatedBy;

        // Act
        entity.Name = "Updated Name";
        entity.Age = 30;
        await _context.SaveChangesAsync();

        // Assert
        // Ověření tracking polí
        Assert.Equal(originalCreatedAt, entity.CreatedAt); // CreatedAt se nemění
        Assert.Equal(originalCreatedBy, entity.CreatedBy); // CreatedBy se nemění
        Assert.True(entity.ModifiedAt > originalCreatedAt); // ModifiedAt se aktualizuje
        Assert.NotNull(entity.ModifiedBy);

        // Ověření audit trail
        var auditTrails = await _context.AuditTrails.OrderBy(a => a.Timestamp).ToListAsync();
        Assert.Equal(2, auditTrails.Count); // Jeden pro Add, jeden pro Update

        var updateAudit = auditTrails.Last();
        Assert.Equal("Modified", updateAudit.Operation);
        Assert.Contains("Name", updateAudit.ChangesJson);
        Assert.Contains("Age", updateAudit.ChangesJson);
        Assert.Contains("Original Name", updateAudit.ChangesJson);
        Assert.Contains("Updated Name", updateAudit.ChangesJson);
    }

    [Fact]
    public async Task SampleEntity_TrackingFields_ShouldBeSetAutomatically()
    {
        // Arrange
        var entity = new SampleEntity
        {
            Id = 0, // Bude nastaveno automaticky při uložení
            Name = "Test Entity",
            Description = "Test Description",
            Age = 30,
            DateOfBirth = new DateTime(1993, 5, 15),
            IsActive = true
        };

        // Act
        _context.SampleEntity.Add(entity);
        await _context.SaveChangesAsync();

        // Assert
        Assert.NotNull(entity.CreatedAt);
        Assert.NotNull(entity.CreatedBy);
        Assert.NotNull(entity.ModifiedAt);
        Assert.NotNull(entity.ModifiedBy);
        Assert.Equal(entity.CreatedAt, entity.ModifiedAt); // Pro novou entitu jsou stejné
        Assert.Equal(entity.CreatedBy, entity.ModifiedBy); // Pro novou entitu jsou stejné
    }

    public void Dispose()
    {
        _context?.Dispose();
        _serviceProvider?.Dispose();
    }
}
