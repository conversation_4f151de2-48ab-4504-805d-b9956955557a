using Domain.Entities;
using NetArchTest.Rules;
using SharedKernel.Abstractions;
using Xunit;

namespace Architecture.Tests;

public class LayerPatternTests
{
    [Fact]
    public void Domain_Entities_Should_Inherit_From_BaseEntity()
    {
        // Arrange
        var assembly = typeof(SampleEntity).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .That()
            .HaveNameEndingWith("Entity")
            .And()
            .DoNotResideInNamespace("Domain.Core")
            .Should()
            .Inherit(typeof(BaseEntity<>))
            .Or()
            .Inherit(typeof(BaseTrackableEntity<>))
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, "Domain entities should inherit from BaseEntity or BaseTrackableEntity");
    }

    [Fact]
    public void Application_Handlers_Should_Follow_Naming_Convention()
    {
        // Arrange
        var assembly = typeof(Application.DependencyInjection).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .That()
            .ImplementInterface(typeof(Application.Abstraction.Mediator.IRequestHandler<,>))
            .Should()
            .HaveNameEndingWith("Handler")
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, "Request handlers should have names ending with 'Handler'");
    }

    [Fact]
    public void Domain_Events_Should_Inherit_From_DomainEvent()
    {
        // Arrange
        var assembly = typeof(SampleEntity).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .That()
            .HaveNameEndingWith("Event")
            .And()
            .DoNotResideInNamespace("Domain.Core")
            .Should()
            .Inherit(typeof(DomainEvent))
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, "Domain events should inherit from DomainEvent");
    }

    [Fact]
    public void Application_Queries_Should_Follow_Naming_Convention()
    {
        // Arrange
        var assembly = typeof(Application.DependencyInjection).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .That()
            .ResideInNamespaceContaining("Queries")
            .Should()
            .HaveNameEndingWith("Query")
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, "Query classes should have names ending with 'Query'");
    }

    [Fact]
    public void Application_Commands_Should_Follow_Naming_Convention()
    {
        // Arrange
        var assembly = typeof(Application.DependencyInjection).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .That()
            .ResideInNamespaceContaining("Commands")
            .Should()
            .HaveNameEndingWith("Command")
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, "Command classes should have names ending with 'Command'");
    }
}
